# 龙腾科技 - 中国风科技官网

一个融合中华传统文化元素与现代科技设计的企业官网模板。

## 🎨 设计特色

### 中国风元素
- **配色方案**: 采用中国红(#c41e3a)和金色(#ffd700)作为主色调
- **字体选择**: 使用 Noto Sans SC 中文字体，确保最佳的中文显示效果
- **文化符号**: 融入龙、太极、山水等中国传统文化元素
- **设计理念**: 体现"厚德载物，自强不息"的企业精神

### 现代科技感
- **响应式设计**: 完美适配桌面、平板和移动设备
- **动画效果**: 丰富的CSS3动画和jQuery交互效果
- **用户体验**: 平滑滚动、悬停效果、渐入动画等现代交互设计

## 🛠️ 技术栈

- **HTML5**: 语义化标签，SEO友好
- **CSS3**: 现代CSS特性，包括Grid、Flexbox、动画等
- **Bootstrap 5**: 响应式框架，快速布局
- **jQuery**: JavaScript库，丰富的交互效果
- **Font Awesome**: 图标库
- **Google Fonts**: 中文字体支持

## 📱 功能特性

### 页面结构
1. **首页横幅** - 震撼的全屏展示区域
2. **关于我们** - 企业介绍和核心理念
3. **服务项目** - 三大核心服务展示
4. **产品展示** - 产品特性和技术亮点
5. **团队介绍** - 核心团队成员展示
6. **联系我们** - 完整的联系表单和信息

### 交互功能
- ✅ 平滑滚动导航
- ✅ 响应式菜单
- ✅ 滚动动画效果
- ✅ 悬停交互效果
- ✅ 表单验证
- ✅ 返回顶部按钮
- ✅ 视差滚动效果
- ✅ 浮动装饰元素

## 🚀 快速开始

1. **下载文件**
   ```bash
   # 确保所有文件都在正确的目录结构中
   chinese-website/
   ├── index.html
   ├── css/
   │   └── style.css
   ├── js/
   │   └── script.js
   └── README.md
   ```

2. **打开网站**
   - 直接在浏览器中打开 `index.html` 文件
   - 或者使用本地服务器（推荐）

3. **自定义内容**
   - 修改 `index.html` 中的文本内容
   - 调整 `css/style.css` 中的样式
   - 在 `js/script.js` 中添加自定义功能

## 🎯 自定义指南

### 修改配色
在 `css/style.css` 文件的 `:root` 选择器中修改CSS变量：
```css
:root {
    --primary-red: #c41e3a;    /* 主红色 */
    --primary-gold: #ffd700;   /* 主金色 */
    --dark-red: #8b0000;       /* 深红色 */
    --light-gold: #fff8dc;     /* 浅金色 */
}
```

### 更换内容
- **公司名称**: 搜索并替换 "龙腾科技"
- **联系信息**: 修改联系我们部分的地址、电话、邮箱
- **服务项目**: 更新服务卡片的图标、标题和描述
- **团队成员**: 替换团队成员的姓名、职位和介绍

### 添加图片
- 将图片文件放在 `images/` 目录中
- 在HTML中替换占位符图标为实际图片
- 更新CSS中的背景图片路径

## 📱 浏览器兼容性

- ✅ Chrome (推荐)
- ✅ Firefox
- ✅ Safari
- ✅ Edge
- ⚠️ IE11+ (部分CSS3特性可能不支持)

## 📄 许可证

本项目仅供学习和参考使用。

## 🤝 贡献

欢迎提交问题和改进建议！

---

**龙腾科技** - 传承千年智慧，引领科技未来
