$(document).ready(function() {
    
    // 平滑滚动
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if( target.length ) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 70
            }, 1000);
        }
    });

    // 导航栏滚动效果
    $(window).scroll(function() {
        var scroll = $(window).scrollTop();
        
        if (scroll >= 50) {
            $('.chinese-nav').addClass('scrolled');
        } else {
            $('.chinese-nav').removeClass('scrolled');
        }
        
        // 滚动动画
        $('.fade-in').each(function() {
            var elementTop = $(this).offset().top;
            var elementBottom = elementTop + $(this).outerHeight();
            var viewportTop = $(window).scrollTop();
            var viewportBottom = viewportTop + $(window).height();
            
            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                $(this).addClass('visible');
            }
        });
    });

    // 为元素添加滚动动画类
    $('.service-card, .feature-item, .about-content').addClass('fade-in');

    // 服务卡片悬停效果
    $('.service-card').hover(
        function() {
            $(this).find('.service-icon').addClass('animate__animated animate__pulse');
        },
        function() {
            $(this).find('.service-icon').removeClass('animate__animated animate__pulse');
        }
    );

    // 数字计数动画
    function animateCounter(element, target, duration) {
        var start = 0;
        var increment = target / (duration / 16);
        var timer = setInterval(function() {
            start += increment;
            if (start >= target) {
                start = target;
                clearInterval(timer);
            }
            $(element).text(Math.floor(start));
        }, 16);
    }

    // 当滚动到统计部分时触发计数动画
    var countersTriggered = false;
    $(window).scroll(function() {
        if (!countersTriggered) {
            $('.counter').each(function() {
                var elementTop = $(this).offset().top;
                var viewportBottom = $(window).scrollTop() + $(window).height();
                
                if (elementTop < viewportBottom) {
                    countersTriggered = true;
                    var target = parseInt($(this).data('target'));
                    animateCounter(this, target, 2000);
                }
            });
        }
    });

    // 导航栏活动状态
    $(window).scroll(function() {
        var scrollPos = $(document).scrollTop();
        
        $('.navbar-nav .nav-link').each(function() {
            var currLink = $(this);
            var refElement = $(currLink.attr("href"));
            
            if (refElement.position() && refElement.position().top <= scrollPos + 100 && refElement.position().top + refElement.height() > scrollPos) {
                $('.navbar-nav .nav-link').removeClass("active");
                currLink.addClass("active");
            } else {
                currLink.removeClass("active");
            }
        });
    });

    // 移动端菜单自动关闭
    $('.navbar-nav .nav-link').on('click', function() {
        if ($(window).width() < 992) {
            $('.navbar-collapse').collapse('hide');
        }
    });

    // 页面加载动画
    $(window).on('load', function() {
        $('.hero-content').addClass('animate__animated animate__fadeInUp');
        $('.hero-decoration').addClass('animate__animated animate__fadeInRight');
    });

    // 鼠标跟随效果（可选）
    var mouseX = 0, mouseY = 0;
    var xp = 0, yp = 0;
    
    $(document).mousemove(function(e) {
        mouseX = e.pageX;
        mouseY = e.pageY;
    });
    
    setInterval(function() {
        xp += ((mouseX - xp) / 6);
        yp += ((mouseY - yp) / 6);
        
        $('.mouse-follower').css({
            left: xp + 'px',
            top: yp + 'px'
        });
    }, 20);

    // 表单验证和提交
    $('#contactForm').on('submit', function(e) {
        e.preventDefault();
        
        var name = $('#name').val();
        var email = $('#email').val();
        var message = $('#message').val();
        
        if (name && email && message) {
            // 这里可以添加实际的表单提交逻辑
            alert('感谢您的留言！我们会尽快与您联系。');
            this.reset();
        } else {
            alert('请填写所有必填字段。');
        }
    });

    // 返回顶部按钮
    $(window).scroll(function() {
        if ($(this).scrollTop() > 300) {
            $('.back-to-top').fadeIn();
        } else {
            $('.back-to-top').fadeOut();
        }
    });

    $('.back-to-top').click(function() {
        $('html, body').animate({scrollTop: 0}, 800);
        return false;
    });

    // 中国风装饰元素动画
    function createFloatingElements() {
        for (let i = 0; i < 5; i++) {
            setTimeout(function() {
                var element = $('<div class="floating-element"></div>');
                element.css({
                    position: 'fixed',
                    width: '4px',
                    height: '4px',
                    background: '#ffd700',
                    borderRadius: '50%',
                    left: Math.random() * window.innerWidth + 'px',
                    top: window.innerHeight + 'px',
                    opacity: 0.6,
                    zIndex: 1,
                    pointerEvents: 'none'
                });
                
                $('body').append(element);
                
                element.animate({
                    top: -50 + 'px',
                    opacity: 0
                }, 8000 + Math.random() * 4000, function() {
                    $(this).remove();
                });
            }, i * 2000);
        }
    }

    // 每10秒创建一次浮动元素
    setInterval(createFloatingElements, 10000);
    createFloatingElements(); // 立即创建一次

    // 视差滚动效果
    $(window).scroll(function() {
        var scrolled = $(window).scrollTop();
        var parallax = $('.chinese-pattern');
        var speed = scrolled * 0.5;
        
        parallax.css('transform', 'translateY(' + speed + 'px)');
    });

    // 打字机效果
    function typeWriter(element, text, speed) {
        var i = 0;
        function type() {
            if (i < text.length) {
                $(element).text($(element).text() + text.charAt(i));
                i++;
                setTimeout(type, speed);
            }
        }
        type();
    }

    // 当页面加载完成后，为某些元素添加打字机效果
    setTimeout(function() {
        if ($('.typewriter').length) {
            $('.typewriter').each(function() {
                var text = $(this).text();
                $(this).text('');
                typeWriter(this, text, 100);
            });
        }
    }, 1000);

});
