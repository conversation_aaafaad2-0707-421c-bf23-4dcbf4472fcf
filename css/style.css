/* 中国风科技网站样式 */

/* 基础设置 */
:root {
  --primary-red: #c41e3a;
  --primary-gold: #ffd700;
  --dark-red: #8b0000;
  --light-gold: #fff8dc;
  --text-dark: #2c3e50;
  --text-light: #ecf0f1;
  --gradient-bg: linear-gradient(135deg, #c41e3a 0%, #8b0000 100%);
  --shadow: 0 10px 30px rgba(196, 30, 58, 0.3);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Noto Sans SC", sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  overflow-x: hidden;
}

/* 导航栏样式 */
.chinese-nav {
  background: var(--gradient-bg);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
}

.chinese-logo {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-gold) !important;
  text-decoration: none;
}

.navbar-nav .nav-link {
  color: var(--text-light) !important;
  font-weight: 500;
  margin: 0 10px;
  position: relative;
  transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
  color: var(--primary-gold) !important;
  transform: translateY(-2px);
}

.navbar-nav .nav-link::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--primary-gold);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
  width: 100%;
}

/* 首页横幅 */
.hero-section {
  height: 100vh;
  background: linear-gradient(rgba(196, 30, 58, 0.8), rgba(139, 0, 0, 0.8)),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="chinese-pattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="2" fill="%23ffd700" opacity="0.3"/></pattern></defs><rect width="100%" height="100%" fill="url(%23chinese-pattern)"/></svg>');
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    transparent 0%,
    rgba(0, 0, 0, 0.3) 100%
  );
}

.hero-content {
  position: relative;
  z-index: 2;
  color: white;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.3rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  font-weight: 300;
}

.chinese-btn {
  background: var(--gradient-bg);
  border: none;
  padding: 12px 30px;
  border-radius: 50px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: var(--shadow);
}

.chinese-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(196, 30, 58, 0.4);
}

.hero-decoration {
  position: relative;
  height: 400px;
}

.chinese-pattern {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, var(--primary-gold) 2px, transparent 2px);
  background-size: 30px 30px;
  border-radius: 50%;
  opacity: 0.3;
  animation: rotate 20s linear infinite;
  margin: 50px auto;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  font-size: 1.5rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* 章节标题样式 */
.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
  position: relative;
}

.title-decoration {
  width: 80px;
  height: 4px;
  background: var(--gradient-bg);
  margin: 0 auto 1rem;
  border-radius: 2px;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #666;
  font-weight: 300;
}

/* 关于我们部分 */
.about-content h3 {
  color: var(--primary-red);
  font-weight: 600;
}

.feature-item {
  text-align: center;
  padding: 20px;
  margin-bottom: 20px;
}

.feature-item i {
  font-size: 2.5rem;
  display: block;
}

.feature-item h5 {
  color: var(--text-dark);
  font-weight: 600;
  margin-bottom: 10px;
}

.about-image {
  text-align: center;
}

.image-placeholder {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 10px;
  padding: 60px 20px;
  color: #6c757d;
}

/* 服务卡片 */
.service-card {
  background: white;
  border-radius: 15px;
  padding: 40px 30px;
  text-align: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 100%;
  border: 1px solid #f0f0f0;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow);
}

.service-icon {
  width: 80px;
  height: 80px;
  background: var(--gradient-bg);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  transition: all 0.3s ease;
}

.service-icon i {
  font-size: 2rem;
  color: white;
}

.service-card:hover .service-icon {
  transform: scale(1.1);
}

.service-card h4 {
  color: var(--text-dark);
  font-weight: 600;
  margin-bottom: 15px;
}

.service-card p {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
}

.service-link {
  color: var(--primary-red);
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.service-link:hover {
  color: var(--dark-red);
  text-decoration: none;
}

.service-link i {
  transition: all 0.3s ease;
}

.service-link:hover i {
  transform: translateX(5px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .chinese-pattern {
    width: 200px;
    height: 200px;
  }

  .hero-decoration {
    height: 250px;
  }
}

/* 产品卡片 */
.product-card {
  background: white;
  border-radius: 15px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 100%;
  border: 1px solid #f0f0f0;
  position: relative;
  overflow: hidden;
}

.product-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 215, 0, 0.1),
    transparent
  );
  transition: all 0.6s ease;
}

.product-card:hover::before {
  left: 100%;
}

.product-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow);
}

.product-image {
  margin-bottom: 20px;
  position: relative;
  z-index: 2;
}

.product-content {
  position: relative;
  z-index: 2;
}

.product-content h4 {
  color: var(--text-dark);
  font-weight: 600;
  margin-bottom: 15px;
}

.product-content p {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
}

.product-features {
  margin-top: 15px;
}

.product-features .badge {
  font-size: 0.8rem;
  padding: 5px 10px;
  margin-bottom: 5px;
}

/* 团队卡片 */
.team-card {
  background: white;
  border-radius: 15px;
  padding: 30px 20px;
  text-align: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 100%;
  border: 1px solid #f0f0f0;
  position: relative;
  overflow: hidden;
}

.team-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow);
}

.team-image {
  width: 100px;
  height: 100px;
  background: #f8f9fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  border: 3px solid var(--primary-gold);
  transition: all 0.3s ease;
}

.team-card:hover .team-image {
  transform: scale(1.1);
  border-color: var(--primary-red);
}

.team-content h4 {
  color: var(--text-dark);
  font-weight: 600;
  margin-bottom: 5px;
}

.team-position {
  color: var(--primary-red);
  font-weight: 500;
  margin-bottom: 15px;
  font-size: 0.9rem;
}

.team-description {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 20px;
}

.team-social {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.team-social a {
  width: 35px;
  height: 35px;
  background: var(--gradient-bg);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all 0.3s ease;
}

.team-social a:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(196, 30, 58, 0.4);
}

/* 联系表单 */
.contact-form .form-control {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 12px 15px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.contact-form .form-control:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--primary-gold);
  box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
  color: white;
}

.contact-form .form-control::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.contact-info {
  padding: 20px;
}

.contact-info h5 {
  color: var(--primary-gold);
  font-weight: 600;
  margin-bottom: 15px;
}

.contact-info p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0;
}

/* 返回顶部按钮 */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background: var(--gradient-bg);
  color: white;
  border-radius: 50%;
  display: none;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: var(--shadow);
}

.back-to-top:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(196, 30, 58, 0.4);
}

/* 社交链接 */
.social-links a {
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.social-links a:hover {
  color: var(--primary-gold) !important;
  transform: translateY(-2px);
}

/* 滚动动画 */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}
